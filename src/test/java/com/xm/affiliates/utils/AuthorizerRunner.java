package com.xm.affiliates.utils;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2CustomAuthorizerEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xm.affiliates.lambda.Authorizer;
import java.io.File;
import java.io.IOException;

public class AuthorizerRunner {

    public static void main(String[] args) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        final File eventFile = new File(args[0]);
        APIGatewayV2CustomAuthorizerEvent request = objectMapper.readValue(eventFile, APIGatewayV2CustomAuthorizerEvent.class);

        System.out.println("Loaded event from: " + args[0]);
        System.out.println("Route ARN: " + request.getRouteArn());
        System.out.println("Route Key: " + request.getRouteKey());

        Context context = new TestContext();

        Authorizer handler = new Authorizer();
        handler.handleRequest(request, context);
    }
}