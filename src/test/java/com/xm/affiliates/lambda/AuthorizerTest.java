package com.xm.affiliates.lambda;

import static com.amazonaws.services.lambda.runtime.events.IamPolicyResponse.ALLOW;
import static com.amazonaws.services.lambda.runtime.events.IamPolicyResponse.DENY;
import static com.xm.affiliates.config.AppConstants.ANONYMOUS;
import static com.xm.affiliates.utils.TestHelper.buildSessionAttributes;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.LambdaLogger;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2CustomAuthorizerEvent;
import com.amazonaws.services.lambda.runtime.events.IamPolicyResponse;
import com.xm.affiliates.repository.UserSessionRepository;
import com.xm.affiliates.dto.UserSession;
import com.xm.affiliates.config.Application;
import com.xm.affiliates.config.BeanRegistry;
import com.xm.affiliates.utils.TestLambdaLogger;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AuthorizerTest {

    @Mock
    UserSessionRepository userSessionRepository;
    @Mock
    APIGatewayV2CustomAuthorizerEvent event;
    @Mock
    Context context;
    Authorizer authorizer;
    MockedStatic<Application> mockedAppConfig;
    LambdaLogger logger = new TestLambdaLogger();

    @BeforeEach
    void setUp() {
        mockedAppConfig = mockStatic(Application.class);
        BeanRegistry mockBeanRegistry = mock(BeanRegistry.class);
        mockedAppConfig.when(Application::getBeanRegistry).thenReturn(mockBeanRegistry);
        when(mockBeanRegistry.getBean(UserSessionRepository.class)).thenReturn(userSessionRepository);
        authorizer = new Authorizer();

        when(event.getRouteArn()).thenReturn("arn:aws:execute-api:region:account-id:api-id/stage/GET/resource");
    }

    @AfterEach
    void tearDown() {
        mockedAppConfig.close();
    }

    @Test
    void whenSessionCookiePresentAndFoundInRepository_thenAllow() {
        String affiliateSessionId = "abc123";
        when(event.getCookies()).thenReturn(List.of("session=" + affiliateSessionId, "other=val"));
        final UserSession userSession = buildSessionAttributes();
        when(userSessionRepository.findUserSessionBySessionId(affiliateSessionId)).thenReturn(
            Optional.of(userSession));
        IamPolicyResponse response = authorizer.handleRequest(event, context);

        String principalId = response.getPrincipalId();
        final String effect = extractEffect(response);

        assertEquals(userSession.userId().toString(), principalId);
        assertEquals(ALLOW, effect);
    }

    @Test
    void whenSessionCookiePresentAndNotFoundInRepository_thenDeny() {
        String affiliateSessionId = "abc123";
        when(event.getCookies()).thenReturn(List.of("session=" + affiliateSessionId, "other=val"));
        when(userSessionRepository.findUserSessionBySessionId(affiliateSessionId)).thenReturn(Optional.empty());
        IamPolicyResponse response = authorizer.handleRequest(event, context);

        String principalId = response.getPrincipalId();
        final String effect = extractEffect(response);

        assertEquals(ANONYMOUS, principalId);
        assertEquals(DENY, effect);
    }

    @Test
    void whenSessionCookieMissing_thenDeny() {
        when(event.getCookies()).thenReturn(List.of("other=val"));
        IamPolicyResponse response = authorizer.handleRequest(event, context);

        String principalId = response.getPrincipalId();
        final String effect = extractEffect(response);

        assertEquals(ANONYMOUS, principalId);
        assertEquals(DENY, effect);
    }

    @Test
    void whenEmptyCookiesList_thenDeny() {
        when(event.getCookies()).thenReturn(List.of());
        IamPolicyResponse response = authorizer.handleRequest(event, context);

        String principalId = response.getPrincipalId();
        final String effect = extractEffect(response);

        assertEquals(ANONYMOUS, principalId);
        assertEquals(DENY, effect);
    }

    @SuppressWarnings("unchecked")
    private static String extractEffect(final IamPolicyResponse response) {
        Map<String, String> statement = ((Map<String, String>[]) response.getPolicyDocument().get("Statement"))[0];
        return statement.get("Effect");
    }
}
