package com.xm.affiliates.config;

import static com.xm.affiliates.config.AppConstants.EnvVariables.REDIS_SERVERS;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xm.affiliates.repository.UserSessionRepository;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.ConnectionPoolConfig;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;

@Slf4j
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public final class Application {

    private static final BeanRegistry beanRegistry = new BeanRegistry();

    public static BeanRegistry getBeanRegistry() {
        initializeBeans();
        return beanRegistry;
    }

    private static void initializeBeans() {
        final ObjectMapper objectMapper = objectMapper();
        beanRegistry.register(ObjectMapper.class, objectMapper);

        final JedisCluster redisClient = redisClient();
        beanRegistry.register(JedisCluster.class, redisClient);

        final UserSessionRepository userSessionRepository = new UserSessionRepository(redisClient, objectMapper);
        beanRegistry.register(UserSessionRepository.class, userSessionRepository);
    }

    private static JedisCluster redisClient() {
        final String redisServers = System.getenv(REDIS_SERVERS);
        log.info("Redis servers: {}", redisServers);

        final Set<HostAndPort> jedisClusterNodes = Arrays
            .stream(redisServers.split(","))
            .map(HostAndPort::from).collect(Collectors.toSet());
        final ConnectionPoolConfig jedisPoolConfig = new ConnectionPoolConfig();
        jedisPoolConfig.setMaxTotal(10);
        jedisPoolConfig.setMaxIdle(5);
        jedisPoolConfig.setMinIdle(1);
        return new JedisCluster(jedisClusterNodes, jedisPoolConfig);
    }

    private static ObjectMapper objectMapper() {
        return new ObjectMapper();
    }
}
