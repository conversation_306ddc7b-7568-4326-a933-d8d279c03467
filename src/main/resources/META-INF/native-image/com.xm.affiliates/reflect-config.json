[{"name": "com.xm.affiliates.lambda.Authorizer", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredClasses": true, "allPublicClasses": true}, {"name": "com.xm.affiliates.dto.UserSession", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredFields": true, "allPublicFields": true}, {"name": "com.xm.affiliates.dto.UserSession$UserSessionBuilder", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredFields": true, "allPublicFields": true}, {"name": "org.apache.commons.pool2.impl.DefaultEvictionPolicy", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "org.apache.commons.pool2.impl.EvictionPolicy", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "org.apache.commons.pool2.impl.BaseGenericObjectPool", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "org.apache.commons.pool2.impl.GenericObjectPool", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}]