{"version": "2.0", "type": "REQUEST", "routeArn": "arn:aws:execute-api:us-west-2:************:abcde12345/dev/GET/my-resource", "identitySource": ["Bearer eyJraWQiOiJrZXktaWQ...rest_of_token"], "routeKey": "GET /my-resource", "rawPath": "/my-resource", "rawQueryString": "", "cookies": ["session=abc123"], "headers": {"authorization": "Bearer eyJraWQiOiJrZXktaWQ...rest_of_token", "host": "abcde12345.execute-api.us-west-2.amazonaws.com", "x-forwarded-for": "***********", "x-forwarded-proto": "https"}, "queryStringParameters": {}, "requestContext": {"accountId": "************", "apiId": "abcde12345", "domainName": "abcde12345.execute-api.us-west-2.amazonaws.com", "domainPrefix": "abcde12345", "http": {"method": "GET", "path": "/my-resource", "protocol": "HTTP/1.1", "sourceIp": "***********", "userAgent": "Custom User Agent String"}, "requestId": "test-request-id", "routeKey": "GET /my-resource", "stage": "dev", "time": "08/Aug/2025:12:00:00 +0000", "timeEpoch": *************}, "pathParameters": {}, "stageVariables": {}}