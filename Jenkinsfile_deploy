#!/usr/bin/groovy
@Library('xm-pipeline-library@0.2.33') _

def targetEnv = params.environment ?: 'dev'
def artifactVersion = params.artifactVersion
def nodeLabel = targetEnv != 'production' ? 'automation_dev' : 'automation_prod'
def nexusCredentialsId = 'nexusArtifactDeployerUser'
def totalStatus = 'Total status'
def accounts = [
        'dev'       : '************',
        'test'      : '************',
        'staging'   : '************',
        'production': '************',
]
def awsCredentialId = "aws-XM-affiliates-aws-authorizer-lambda-${targetEnv}"
def lambdaName = "affiliates-authorizer"
def applicationName = "affiliates-aws-authorizer-lambda"
def awsRegion = 'eu-west-2'
def s3Bucket = "${accounts[targetEnv]}-affiliate-lambda-artifacts"

def zipName = "${applicationName}-${artifactVersion}.zip"
def zipLatestName = "${applicationName}-latest.zip"


pipeline {
    options {
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '50', artifactNumToKeepStr: '50'))
        gitLabConnection('gitlab')
        gitlabBuilds(builds: [totalStatus])
        ansiColor('xmAnsibleColors')
    }

    agent {
        node {
            label nodeLabel
        }
    }

    stages {

        stage('Preparation') {
            steps {
                script {
                    checkoutMerged('*****************:piktiv/affiliates-aws-authorizer-lambda.git', params.source)
                }
            }
            post {
                success {
                    updateGitlabCommitStatus name: 'Preparation', state: 'success'
                }
                failure {
                    updateGitlabCommitStatus name: 'Preparation', state: 'failed'
                }
            }
        }

        stage('Download from Nexus') {
            steps {
                script {
                    withCredentials([usernamePassword(credentialsId: nexusCredentialsId, usernameVariable: 'nexusUsername', passwordVariable: 'nexusPassword')]) {
                        sh 'chmod +x gradlew'
                        sh "./gradlew downloadZipFromNexus -Pversion=${artifactVersion} -PnexusUsername=\${nexusUsername} -PnexusPassword=\${nexusPassword}"
                    }
                }
            }

            post {
                success {
                    updateGitlabCommitStatus name: 'Download from Nexus', state: 'success'

                }
                failure {
                    updateGitlabCommitStatus name: 'Download from Nexus', state: 'failed'
                }
            }
        }

        stage('Store to S3') {
            steps {
                script {
                    runAWSCommands(awsCredentialId, awsRegion) {
                        sh "aws s3 cp ${zipName} s3://${s3Bucket}/${lambdaName}/${zipName}"
                        sh "aws s3 cp ${zipName} s3://${s3Bucket}/${lambdaName}/${zipLatestName}"
                    }
                }
            }
            post {
                always {
                    sh "rm ${zipName}"
                }
                success {
                    updateGitlabCommitStatus name: 'Store to S3', state: 'success'
                }
                failure {
                    updateGitlabCommitStatus name: 'Store to S3', state: 'failed'
                }
            }
        }

        stage('Deploy') {
            steps {
                script {
                    runAWSCommands(awsCredentialId, awsRegion) {
                        def lambdaConfigJson = sh(
                                script: "aws lambda get-function-configuration --function-name ${lambdaName}",
                                returnStdout: true
                        )
                        def lambdaConfig = readJSON(text: lambdaConfigJson)
                        def lambdaEnvVars = lambdaConfig.Environment
                        def prevArtifact = lambdaEnvVars.Variables.ARTIFACT
                        lambdaEnvVars.Variables.ARTIFACT = zipName

                        def zipLatestFilePath = "${lambdaName}/${zipLatestName}"


                        println "Previous artifact version: ${prevArtifact}"
                        println "environment: ${lambdaEnvVars}"

                        sh "aws lambda update-function-code --function-name ${lambdaName} --s3-bucket ${s3Bucket} --s3-key ${zipLatestFilePath}"
                        sh "aws lambda update-function-configuration --function-name ${lambdaName} --environment '${lambdaEnvVars.toString()}'"
                        def version = sh(script: "aws lambda publish-version --function-name ${lambdaName} --query 'Version'", returnStdout: true).trim()

                        sh "aws lambda update-alias --function-name ${lambdaName} --name LATEST_ALIAS --function-version ${version}"

                        currentBuild.description = currentBuild.description ?: ""
                        currentBuild.description += "\nArtifact: v${version}\n"
                        currentBuild.description += "Environment: ${targetEnv}\n"
                    }
                }
                post {
                    success {
                        updateGitlabCommitStatus name: 'Deploy', state: 'success'
                    }
                    failure {
                        updateGitlabCommitStatus name: 'Deploy', state: 'failed'
                    }
                }
            }
        }
    }

    post {
        cleanup {
            cleanWs()
        }
        success {
            updateGitlabCommitStatus name: totalStatus, state: 'success'
        }
        failure {
            updateGitlabCommitStatus name: totalStatus, state: 'failed'
        }
    }
}
