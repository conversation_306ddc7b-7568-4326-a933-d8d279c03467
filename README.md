# affiliates-aws-authorizer-lambda

This project provides an AWS Lambda authorizer for XM affiliates, built with Java and packaged as a native executable using GraalVM. It is designed to run efficiently in AWS Lambda environments, providing custom authorization logic for API Gateway requests.

## Features
- Custom Lambda authorizer for API Gateway
- Native image build with GraalVM for fast cold starts
- Docker-based build and local test workflow

## Prerequisites
- Docker
- Java 17+
- GraalVM (via provided Docker image)
- AWS CLI (optional, for deployment)

## Build

``` bash
    docker run --rm --platform linux/amd64 \
    -m 8g --memory-swap 8g \
    -v $(pwd):/project \
    -v $HOME/.gradle:/root/.gradle \
    -w /project \
    registry.xm.com/xmdevs/graalvm-xm:22.1.0 \
    -c "chmod +x gradlew && ./gradlew clean nativeCompile"
```

native executable binary will be generated under `build/native/nativeCompile/native`

## Run and test locally

start up the redis-cluster and populate it with some test data

``` bash
    docker compose up -d redis-init
```
!NOTE regarding the following line in compose.yaml

`#CLUSTER_ANNOUNCE_IP: redis-cluster`

* comment in when running authorizer from Intellij
* comment out when running authorizer from docker container

start up the lambda-authorizer container

``` bash
    docker compose up -d lambda-authorizer
```

invoke the Lambda authorizer with a sample event:

``` bash
   curl \
    -X POST http://localhost:9000/2015-03-31/functions/function/invocations \
    -H "Content-Type: application/json" \
    -d @src/main/resources/input/event.json
```