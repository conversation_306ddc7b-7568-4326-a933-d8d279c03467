plugins {
    id 'application'
    id 'maven-publish'
    id 'org.graalvm.buildtools.native'
}

repositories {
    mavenLocal()

    maven {
        url "$nexusRepo/maven-public/"
        credentials {
            username = project.properties.nexusUsername
            password = project.properties.nexusPassword
        }
    }
    maven {
        url "$nexusRepo/releases/"
        credentials {
            username = project.properties.nexusUsername
            password = project.properties.nexusPassword
        }
    }
    mavenCentral()

}

application {
    mainClass.set('com.amazonaws.services.lambda.runtime.api.client.AWSLambda')
}

dependencies {
    implementation "com.amazonaws:aws-lambda-java-core:$awsLambdaJavaCoreVersion"
    implementation "com.amazonaws:aws-lambda-java-events:$awsLambdaJavaEventsVersion"
    implementation "com.amazonaws:aws-lambda-java-runtime-interface-client:$awsLambdaJavaRuntimeInterfaceClientVersion"
    implementation "com.amazonaws:aws-java-sdk-iam:$awsJavaSdkIamVersion"
    implementation "com.amazonaws:aws-java-sdk-ssm:$awsJavaSdkSsmVersion"
    implementation "com.amazonaws:aws-lambda-java-log4j2:$awsLambdaJavaLog4j2Version"
    implementation "ch.qos.logback:logback-classic:$logbackVersion"

    implementation "redis.clients:jedis:$jedisVersion"

    compileOnly "org.projectlombok:lombok:$lombokVersion"
    annotationProcessor "org.projectlombok:lombok:$lombokVersion"


    testImplementation platform("org.junit:junit-bom:$junitJupiterVersion")
    testImplementation "org.junit.jupiter:junit-jupiter"
    testImplementation "org.mockito:mockito-junit-jupiter:$mockitoVersion"
}

graalvmNative {
    binaries {
        main {
            imageName = "native"
            verbose = true
            fallback = false
            buildArgs.add('--verbose')
            buildArgs.add('-J-Xmx8g')
            buildArgs.add('-J-Xms4g')
            buildArgs.add('--native-image-info')
            buildArgs.add('--no-fallback')
            buildArgs.add('--enable-http')
            buildArgs.add('--enable-https')
        }
    }
}

testing {
    suites {
        test {
            useJUnitJupiter()
        }
    }
}

tasks.register('createDistributionZip', Zip) {
    mustRunAfter(tasks.named('nativeCompile')) // Ensure 'nativeCompile' runs before this task logically

    destinationDirectory = layout.buildDirectory.dir("distributions").get().asFile
    from('src/main/resources/bootstrap') {
        into('.')
    }
    from('build/native/nativeCompile/native') {
        into('.')
    }
}

tasks.register("downloadZipFromNexus") {
    doLast {
        def nexusUrl = "$nexusRepo/releases/com/xm/${project.name}/${project.version}/${project.name}-${project.version}.zip"
        def outputFile = file("${project.name}-${project.version}.zip")
        def basicAuth = "${project.properties.nexusUsername}:${project.properties.nexusPassword}".bytes.encodeBase64().toString()

        // Open connection to Nexus
        println("Downloading ZIP from: $nexusUrl")
        def connection = URI.create(nexusUrl).toURL().openConnection() as HttpURLConnection
        connection.setRequestProperty("Authorization", "Basic $basicAuth") // Basic Auth for Nexus
        connection.setRequestMethod("GET")

        if (connection.responseCode == 200) {
            outputFile.bytes = connection.inputStream.bytes
            println("Downloaded ZIP file to: ${outputFile}")
        } else {
            throw new GradleException("Failed to download ZIP file. Response Code: ${connection.responseCode}, Message: ${connection.responseMessage}")
        }
    }
}

publishing {
    publications {
        mavenJava(org.gradle.api.publish.maven.MavenPublication) {
            // Specify the custom ZIP file as the artifact to publish
            artifact(tasks.named("createDistributionZip")) {
                extension = 'zip'
            }
        }
    }
    repositories {
        maven {
            url = "$nexusRepo/releases"
            credentials {
                username = project.properties.nexusUsername
                password = project.properties.nexusPassword
            }
        }
    }
}
